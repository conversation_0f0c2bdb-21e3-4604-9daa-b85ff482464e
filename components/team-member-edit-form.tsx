"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@/lib/api"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { useForm } from "react-hook-form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const roles = [
    {
        id: "68381f3578431cf9a9e1bba2",
        name: "Admin",
    },
    {
        id: "68381f3578431cf9a9e1bba3",
        name: "CEO",
    },
    {
        id: "68381f3578431cf9a9e1bba4",
        name: "Team Leader",
    },
    {
        id: "68381f3578431cf9a9e1bba5",
        name: "Team Member",
    },
]

interface TeamMemberEditFormProps {
    initialData: TeamMember
    onSubmit: (data: Partial<TeamMember>) => void
    onCancel: () => void
}

export default function TeamMemberEditForm({ initialData, onSubmit, onCancel }: TeamMemberEditFormProps) {

    const form = useForm<Partial<TeamMember>>({
        defaultValues: {
            username: initialData.username,
            first_name: initialData.first_name,
            last_name: initialData.last_name,
            email: initialData.email,
            number: initialData.number,
            roles: initialData.roles,
        },
    })
    
    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                    control={form.control}
                    name="username"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Username</FormLabel>
                            <FormControl>
                                <Input {...field} />
                            </FormControl>
                        </FormItem>
                    )}
                />
                <FormField
                    control={form.control}
                    name="first_name"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>First Name</FormLabel>
                            <FormControl>
                                <Input {...field} />
                            </FormControl>
                        </FormItem>
                    )}
                />
                <FormField
                    control={form.control}
                    name="last_name"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Last Name</FormLabel>
                            <FormControl>
                                <Input {...field} />
                            </FormControl>
                        </FormItem>
                    )}
                />
                <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                                <Input {...field} type="email" />
                            </FormControl>
                        </FormItem>
                    )}
                />
                <FormField
                    control={form.control}
                    name="number"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Phone Number</FormLabel>
                            <FormControl>
                                <Input {...field} />
                            </FormControl>
                        </FormItem>
                    )}
                />
                <FormField
                    control={form.control}
                    name="roles"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Role</FormLabel>
                            <Select
                                value={field.value?.[0]?._id || ''}
                                onValueChange={(value) => field.onChange([{ _id: value, name: roles.find(r => r.id === value)?.name || '' }])}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select a role" />
                                </SelectTrigger>
                                <SelectContent>
                                    {roles.map((role) => (
                                        <SelectItem key={role.id} value={role.id}>
                                            {role.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </FormItem>
                    )}
                />

                <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        Cancel
                    </Button>
                    <Button type="submit">Save Changes</Button>
                </div>
            </form>
        </Form>
    )
}
